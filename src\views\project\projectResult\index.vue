<template>
  <div class="app-container">
    <!-- 查询条件表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="query-form">
      <el-form-item label="业务类型" prop="businessTypeId">
        <el-select v-model="queryParams.businessTypeId" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option label="全部" value=""/>
          <el-option v-for="item in businessTypeOptions" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option label="全部" value=""/>
          <el-option v-for="item in statusOptions" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="负责项目经理" prop="projectManagers">
        <el-select v-model="queryParams.projectManagers" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_project_manager" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="项目/任务" prop="projectTaskName">
        <el-input v-model="queryParams.projectTaskName" placeholder="请输入项目/任务名称" clearable style="width: 200px" />
      </el-form-item>

      <el-form-item label="成果类型" prop="resultType">
        <el-select v-model="queryParams.resultType" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_types" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="优先级" prop="priorityLevel">
        <el-select v-model="queryParams.priorityLevel" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option label="全部" value=""/>
          <el-option label="P1" value="P1"/>
          <el-option label="P2" value="P2"/>
          <el-option label="P3" value="P3"/>
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间" prop="createTimeRange">
        <el-date-picker
          v-model="queryParams.createTimeRange"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="请选择时间"
          end-placeholder="请选择时间"
          style="width: 350px">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="所属业务大类" prop="businessCategoryMajor">
        <el-select v-model="queryParams.businessCategoryMajor" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_business_category_major" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="所属业务小类" prop="businessCategoryMinor">
        <el-select v-model="queryParams.businessCategoryMinor" placeholder="请选择" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_business_category_minor" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-sort" size="mini">归档</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini">发送邮件</el-button>
      </el-col>
      <el-col :span="20" style="text-align: right;">
        <el-button type="primary" size="mini" @click="handleBusinessTypeManage">业务类型管理</el-button>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-table 
      v-loading="loading" 
      :data="tableData" 
      @sort-change="handleSortChange" 
      height="calc(100vh - 400px)" 
      stripe 
      border>
      
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="60" align="center" />
      
      <el-table-column label="业务类型" align="center" prop="businessTypeName" width="100" show-overflow-tooltip />
      
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)" size="mini">
            {{ getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="项目/任务名称" align="center" prop="projectTaskName" min-width="150" show-overflow-tooltip />
      
      <el-table-column label="优先级" align="center" prop="priorityLevel" width="100" sortable="custom">
        <template slot-scope="scope">
          <el-tag :type="getPriorityType(scope.row.priorityLevel)" size="mini">
            {{ scope.row.priorityLevel }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="项目里程碑" align="center" prop="milestoneDisplay" min-width="120">
        <template slot-scope="scope">
          <div class="milestone-content" v-html="scope.row.milestoneDisplay"></div>
        </template>
      </el-table-column>
      
      <el-table-column label="任务说明/进度" align="center" prop="progressDisplay" min-width="150">
        <template slot-scope="scope">
          <div class="progress-content" v-html="scope.row.progressDisplay"></div>
        </template>
      </el-table-column>
      
      <el-table-column label="干系人" align="center" prop="stakeholderDisplay" min-width="120">
        <template slot-scope="scope">
          <div class="stakeholder-content" v-html="scope.row.stakeholderDisplay"></div>
        </template>
      </el-table-column>
      
      <el-table-column label="投入人力" align="center" prop="manpowerDisplay" width="100">
        <template slot-scope="scope">
          <div class="manpower-content" v-html="scope.row.manpowerDisplay"></div>
        </template>
      </el-table-column>
      
      <el-table-column label="工作量(人日)" align="center" prop="workloadDisplay" width="120">
        <template slot-scope="scope">
          <div class="workload-content" v-html="scope.row.workloadDisplay"></div>
        </template>
      </el-table-column>
      
      <el-table-column label="需求背景" align="center" prop="requirementBackground" min-width="120" show-overflow-tooltip />
      
      <el-table-column label="负责项目经理" align="center" prop="projectManagers" min-width="100" show-overflow-tooltip />
      
      <el-table-column label="更新时间" align="center" prop="updatedTime" min-width="160" sortable="custom">
        <template slot-scope="scope">
          <span>{{ scope.row.updatedTime | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="完成时间" align="center" prop="completionTime" min-width="160" sortable="custom">
        <template slot-scope="scope">
          <span>{{ scope.row.completionTime | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="所属业务大类" align="center" prop="businessCategoryMajor" width="100">
        <template slot-scope="scope">
          <span>{{ getDictLabel(scope.row.businessCategoryMajor, dict.type.project_outcome_business_category_major) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="所属业务小类" align="center" prop="businessCategoryMinor" width="100">
        <template slot-scope="scope">
          <span>{{ getDictLabel(scope.row.businessCategoryMinor, dict.type.project_outcome_business_category_minor) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="创建人" align="center" prop="createdBy" min-width="100" />
      
      <el-table-column label="创建时间" align="center" prop="createdTime" min-width="160" sortable="custom">
        <template slot-scope="scope">
          <span>{{ scope.row.createdTime | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" width="180" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-refresh" @click="handleSync(scope.row)">同步</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleViewDetail(scope.row)">查看详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" style="color: #f56c6c;">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增编辑弹窗 -->
    <addEditDialog 
      :dialogVisible.sync="addEditDialog.show" 
      :dialogTitle="addEditDialog.title" 
      :dialogData="addEditDialog.data" 
      @callback="handleQuery">
    </addEditDialog>
  </div>
</template>

<script>
// 导入API接口
import {
  projectResultList,
  projectResultDelete,
  projectResultSync,
  getBusinessTypeOptions
} from "@/api/project/projectResult"
import addEditDialog from "./components/addEditDialog.vue"

export default {
  name: "ProjectResult",
  dicts: [
    'project_outcome_types',
    'project_outcome_business_category_major',
    'project_outcome_business_category_minor',
    'project_outcome_project_manager'
  ],
  components: {
    addEditDialog
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [
        // 静态模拟数据
        {
          id: 1,
          businessTypeId: 1,
          businessTypeName: "中台支持",
          status: "3",
          projectTaskName: "商业环境管理平台一期建设—数据平台",
          priorityLevel: "P1",
          milestoneDisplay: "完成评审: 2025-02-07<br/>完成开发: 2025-03-08<br/>完成测试: 2025-03-12<br/>完成上线: 2025-03-13",
          progressDisplay: "需求评审: 100%<br/>开发进度: 100%<br/>测试验收: 100%",
          stakeholderDisplay: "产品: 黄金梅<br/>开发: 工具开发组<br/>测试: 网络运维测试组",
          manpowerDisplay: "开发: 5人<br/>测试: 2人",
          workloadDisplay: "开发: 5人日<br/>测试: 2人日",
          requirementBackground: "商业环境管理需求",
          projectManagers: "黄金梅",
          updatedTime: "2025-01-13 15:30:21",
          completionTime: "2025-03-13 18:00:00",
          businessCategoryMajor: "1",
          businessCategoryMinor: "7",
          createdBy: "黄金梅",
          createdTime: "2025-01-10 09:00:00"
        },
        {
          id: 2,
          businessTypeId: 2,
          businessTypeName: "业务系统",
          status: "2",
          projectTaskName: "新建环境规则管理",
          priorityLevel: "P2",
          milestoneDisplay: "完成评审: 2025-05-22<br/>完成开发: 2025-06-06",
          progressDisplay: "需求评审: 100%<br/>开发进度: 100%<br/>测试验收: 30%",
          stakeholderDisplay: "产品: 腾跳升<br/>开发: 容器开发组<br/>测试: 前、后端测试二组",
          manpowerDisplay: "开发: 2人<br/>测试: 3人",
          workloadDisplay: "开发: 3人日<br/>测试: 1人日",
          requirementBackground: "环境管理优化需求",
          projectManagers: "腾跳升",
          updatedTime: "2025-01-13 14:20:15",
          completionTime: null,
          businessCategoryMajor: "1",
          businessCategoryMinor: "1",
          createdBy: "腾跳升",
          createdTime: "2025-01-08 10:30:00"
        }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        businessTypeId: null,
        status: "",
        projectManagers: null,
        projectTaskName: "",
        resultType: null,
        businessCategoryMajor: null,
        businessCategoryMinor: null,
        priorityLevel: "",
        archiveFlag: null,
        createTimeRange: []
      },
      // 弹窗配置
      addEditDialog: {
        show: false,
        title: '新增',
        data: {}
      },
      // 业务类型选项（静态数据，后续替换为动态获取）
      businessTypeOptions: [
        { value: "software", label: "软件" },
        { value: "finance", label: "金融" },
        { value: "ecommerce", label: "电商" }
      ],
      // 状态选项
      statusOptions: [
        { value: "1", label: "未开始" },
        { value: "2", label: "进行中" },
        { value: "3", label: "已完成" },
        { value: "4", label: "已取消" }
      ],

    }
  },
  created() {
    this.getList()
    this.loadOptions()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      
      // 处理时间范围参数和分页参数
      let params = { ...this.queryParams }
      if (this.queryParams.createTimeRange && this.queryParams.createTimeRange.length !== 0) {
        params.createTimeStart = this.queryParams.createTimeRange[0]
        params.createTimeEnd = this.queryParams.createTimeRange[1]
      }
      delete params.createTimeRange
      
      // 清理空值参数
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key]
        }
      })
      
      // 实际的API调用
      projectResultList(params).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          this.tableData = res.rows || []
        } else {
          this.$message.error(res.msg || '查询失败')
          // 如果API调用失败，使用静态数据作为备选
          this.total = this.tableData.length
        }
      }).catch(() => {
        this.loading = false
        // 网络异常时使用静态数据
        this.total = this.tableData.length
        this.$message.warning('接口调用失败，显示静态数据')
      })
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    
    /** 业务类型管理 */
    handleBusinessTypeManage() {
      this.$message.info('业务类型管理功能待开发')
      // TODO: 跳转到业务类型管理页面
    },
    
    /** 新增 */
    handleAdd() {
      this.addEditDialog.show = true
      this.addEditDialog.title = '新增项目成果'
      this.addEditDialog.data = {}
    },
    
    /** 编辑 */
    handleEdit(row) {
      this.addEditDialog.show = true
      this.addEditDialog.title = '编辑项目成果'
      this.addEditDialog.data = { ...row }
    },
    
    /** 同步 */
    handleSync(row) {
      this.$confirm(`确认同步项目"${row.projectTaskName}"的数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        projectResultSync(row.id).then(res => {
          if (res.code === 200) {
            this.$message.success('同步成功')
            this.getList()
          } else {
            this.$message.error(res.msg || '同步失败')
          }
        }).catch(() => {
          this.$message.error('同步失败')
        })
      }).catch(() => {
        this.$message.info('已取消同步')
      })
    },
    
    /** 查看详情 */
    handleViewDetail(row) {
      this.$message.info(`查看详情：${row.projectTaskName}`)
      // TODO: 跳转到详情页面或打开详情弹窗
    },
    
    /** 删除 */
    handleDelete(row) {
      this.$confirm(`确认删除项目"${row.projectTaskName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        projectResultDelete(row.id).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg || '删除失败')
          }
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    
    /** 排序 */
    handleSortChange({ column, prop, order }) {
      this.queryParams.orderByColumn = prop
      this.queryParams.isAsc = order
      this.handleQuery()
    },
    

    
    /** 获取状态标签 */
    getStatusLabel(value) {
      const item = this.statusOptions.find(item => item.value === value)
      return item ? item.label : value
    },
    
    /** 获取状态类型 */
    getStatusType(status) {
      const typeMap = {
        '1': 'info',
        '2': 'warning', 
        '3': 'success',
        '4': 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    /** 获取优先级类型 */
    getPriorityType(priority) {
      const typeMap = {
        'P1': 'danger',
        'P2': 'warning',
        'P3': 'info'
      }
      return typeMap[priority] || 'info'
    },
    
    /** 根据value获取字典label */
    getDictLabel(value, dictData) {
      const item = dictData.find(item => item.value === value)
      return item ? item.label : value
    },

    /** 获取业务类型标签 */
    getBusinessTypeLabel(value) {
      const item = this.businessTypeOptions.find(item => item.value === value)
      return item ? item.label : value
    },

    /** 加载下拉选项数据 */
    loadOptions() {
      // 加载业务类型选项
      getBusinessTypeOptions().then(res => {
        if (res.code === 200) {
          this.businessTypeOptions = res.data
        }
      }).catch(() => {
        console.warn('获取业务类型选项失败，使用默认数据')
      })
    }

  }
}
</script>

<style scoped>
.query-form {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.milestone-content {
  line-height: 1.5;
}

.milestone-item {
  margin-bottom: 4px;
}

.milestone-text {
  display: inline-block;
  margin-right: 8px;
}

.milestone-date {
  color: #666;
  font-size: 12px;
}

.progress-content {
  text-align: left;
}

.progress-text {
  margin-bottom: 8px;
  font-weight: bold;
}

.progress-details {
  font-size: 12px;
  color: #666;
}

.progress-details > div {
  margin-bottom: 2px;
}

/* 表格单元格内容样式 */
.el-table .cell {
  line-height: 1.4;
}

/* 操作按钮样式 */
.small-padding .cell {
  padding-left: 8px;
  padding-right: 8px;
}
</style>
